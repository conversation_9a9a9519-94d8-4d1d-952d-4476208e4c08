// Supabase Configuration
// Replace these with your actual Supabase project URL and anon key
const SUPABASE_URL = 'https://pabikxyftoymhvzdqbhz.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBhYmlreHlmdG95bWh2emRxYmh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5Njk5MzUsImV4cCI6MjA2NzU0NTkzNX0.Wl8w02Q5XfDEqRvoIE4uobXXUXo3NJhCY8ZcjyYs1Xg';

// Initialize Supabase client (supabase is loaded from CDN)
const supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Database table names
const TABLES = {
    INVOICES: 'invoices',
    CUSTOMERS: 'customers',
    INVOICE_ITEMS: 'invoice_items'
};

// Export for use in other modules
window.supabaseClient = supabaseClient;
window.TABLES = TABLES;
