/* Global Styles */
:root {
    --primary-color: #4a6fa5;
    --secondary-color: #166088;
    --accent-color: #4fc3dc;
    --background-color: #f8f9fa;
    --text-color: #333;
    --light-gray: #e9ecef;
    --medium-gray: #ced4da;
    --dark-gray: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

/* Form Styles */
.form-container {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
}

.form-header {
    background-color: var(--primary-color);
    color: white;
    padding: 25px 20px;
    text-align: center;
}

.form-header h1 {
    margin-bottom: 5px;
    font-size: 28px;
}

.form-header p {
    opacity: 0.8;
    font-size: 16px;
}

.tabs {
    display: flex;
    background-color: var(--light-gray);
}

.tab {
    flex: 1;
    padding: 15px;
    text-align: center;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    color: var(--dark-gray);
    transition: var(--transition);
}

.tab.active {
    background-color: white;
    color: var(--primary-color);
    border-bottom: 3px solid var(--primary-color);
}

.form-content {
    padding: 30px 20px;
}

.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-group label {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--dark-gray);
}

.form-group input {
    width: 100%;
    padding: 12px 12px 12px 45px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
}

.form-group input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.2);
}

.btn-primary {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
}

/* Hide signup form by default */
#signup-form {
    display: none;
}

/* Dashboard Styles */
.dashboard-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.dashboard-header {
    background-color: var(--primary-color);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-header h1 {
    font-size: 24px;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-info span {
    margin-right: 15px;
}

.logout-btn {
    background: none;
    border: 1px solid white;
    color: white;
    padding: 8px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.logout-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.dashboard-content {
    padding: 20px;
}

.add-task-form {
    margin-bottom: 30px;
    background-color: var(--light-gray);
    padding: 20px;
    border-radius: var(--border-radius);
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.form-row .form-group label {
    position: static;
    display: block;
    margin-bottom: 5px;
    transform: none;
}

.form-row .form-group input {
    padding: 12px;
}

.task-list {
    list-style: none;
}

.task-item {
    background-color: white;
    border: 1px solid var(--light-gray);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
}

.task-item:hover {
    box-shadow: var(--box-shadow);
}

.task-info {
    flex: 1;
}

.task-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}

.task-deadline {
    color: var(--dark-gray);
    font-size: 14px;
}

.task-actions {
    display: flex;
    gap: 10px;
}

.task-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 18px;
    transition: var(--transition);
}

.complete-btn {
    color: var(--success-color);
}

.delete-btn {
    color: var(--danger-color);
}

.task-item.completed .task-title {
    text-decoration: line-through;
    color: var(--dark-gray);
}

.task-item.completed {
    background-color: rgba(40, 167, 69, 0.1);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 10px;
    }
    
    .dashboard-header {
        flex-direction: column;
        text-align: center;
    }
    
    .user-info {
        margin-top: 10px;
        margin-bottom: 10px;
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mt-20 {
    margin-top: 20px;
}

.mb-20 {
    margin-bottom: 20px;
}

.hidden {
    display: none;
}

.error-message {
    color: var(--danger-color);
    font-size: 14px;
    margin-top: 5px;
}

.success-message {
    color: var(--success-color);
    font-size: 14px;
    margin-top: 5px;
}