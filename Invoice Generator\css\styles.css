/* Global Styles */
:root {
    --primary-color: #4a6fa5;
    --secondary-color: #166088;
    --accent-color: #4fc3dc;
    --background-color: #f8f9fa;
    --text-color: #333;
    --light-gray: #e9ecef;
    --medium-gray: #ced4da;
    --dark-gray: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

/* Form Styles */
.form-container {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
}

.form-header {
    background-color: var(--primary-color);
    color: white;
    padding: 25px 20px;
    text-align: center;
}

.form-header h1 {
    margin-bottom: 5px;
    font-size: 28px;
}

.form-header p {
    opacity: 0.8;
    font-size: 16px;
}

.tabs {
    display: flex;
    background-color: var(--light-gray);
}

.tab {
    flex: 1;
    padding: 15px;
    text-align: center;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    color: var(--dark-gray);
    transition: var(--transition);
}

.tab.active {
    background-color: white;
    color: var(--primary-color);
    border-bottom: 3px solid var(--primary-color);
}

.form-content {
    padding: 30px 20px;
}

.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-group label {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--dark-gray);
}

.form-group input {
    width: 100%;
    padding: 12px 12px 12px 45px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
}

.form-group input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.2);
}

.btn-primary {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
}

/* Hide signup form by default */
#signup-form {
    display: none;
}

/* Dashboard Styles */
.dashboard-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.dashboard-header {
    background-color: var(--primary-color);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-header h1 {
    font-size: 24px;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-info span {
    margin-right: 15px;
}

.logout-btn {
    background: none;
    border: 1px solid white;
    color: white;
    padding: 8px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.logout-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.dashboard-content {
    padding: 20px;
}

.add-task-form {
    margin-bottom: 30px;
    background-color: var(--light-gray);
    padding: 20px;
    border-radius: var(--border-radius);
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.form-row .form-group label {
    position: static;
    display: block;
    margin-bottom: 5px;
    transform: none;
}

.form-row .form-group input {
    padding: 12px;
}

.task-list {
    list-style: none;
}

.task-item {
    background-color: white;
    border: 1px solid var(--light-gray);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
}

.task-item:hover {
    box-shadow: var(--box-shadow);
}

.task-info {
    flex: 1;
}

.task-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}

.task-deadline {
    color: var(--dark-gray);
    font-size: 14px;
}

.task-actions {
    display: flex;
    gap: 10px;
}

.task-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 18px;
    transition: var(--transition);
}

.complete-btn {
    color: var(--success-color);
}

.delete-btn {
    color: var(--danger-color);
}

.task-item.completed .task-title {
    text-decoration: line-through;
    color: var(--dark-gray);
}

.task-item.completed {
    background-color: rgba(40, 167, 69, 0.1);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 10px;
    }
    
    .dashboard-header {
        flex-direction: column;
        text-align: center;
    }
    
    .user-info {
        margin-top: 10px;
        margin-bottom: 10px;
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mt-20 {
    margin-top: 20px;
}

.mb-20 {
    margin-bottom: 20px;
}

.hidden {
    display: none;
}

.error-message {
    color: var(--danger-color);
    font-size: 14px;
    margin-top: 5px;
}

.success-message {
    color: var(--success-color);
    font-size: 14px;
    margin-top: 5px;
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

.stat-info h3 {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
    color: var(--text-color);
}

.stat-info p {
    color: var(--dark-gray);
    font-size: 14px;
}

/* Invoices Container */
.invoices-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.invoices-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--light-gray);
}

.invoices-header h2 {
    margin: 0;
    color: var(--text-color);
}

/* Invoices Table */
.invoices-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.invoices-table th,
.invoices-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--light-gray);
}

.invoices-table th {
    background-color: var(--background-color);
    font-weight: 600;
    color: var(--text-color);
    font-size: 14px;
}

.invoices-table td {
    font-size: 14px;
}

.invoices-table tbody tr:hover {
    background-color: var(--background-color);
}

/* Status Badges */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-draft {
    background-color: #f8f9fa;
    color: #6c757d;
}

.status-sent {
    background-color: #e3f2fd;
    color: #1976d2;
}

.status-paid {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.status-overdue {
    background-color: #ffebee;
    color: #c62828;
}

.status-cancelled {
    background-color: #fafafa;
    color: #424242;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 5px;
}

.btn-icon {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--dark-gray);
    transition: var(--transition);
    font-size: 14px;
}

.btn-icon:hover {
    background-color: var(--light-gray);
    color: var(--primary-color);
}

.btn-icon.btn-danger:hover {
    background-color: #ffebee;
    color: var(--danger-color);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--dark-gray);
}

.empty-state i {
    color: var(--medium-gray);
    margin-bottom: 20px;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: var(--text-color);
}

.empty-state p {
    margin-bottom: 20px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow-y: auto;
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    border-radius: var(--border-radius);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--light-gray);
    background-color: var(--background-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-header h2 {
    margin: 0;
    color: var(--text-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--dark-gray);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.modal-close:hover {
    background-color: var(--light-gray);
    color: var(--text-color);
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid var(--light-gray);
    background-color: var(--background-color);
}

/* Form Sections */
.form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--light-gray);
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h3 {
    margin-bottom: 15px;
    color: var(--text-color);
    font-size: 18px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.section-header h3 {
    margin: 0;
}

/* Customer Form */
.customer-form {
    background-color: var(--background-color);
    padding: 15px;
    border-radius: var(--border-radius);
    margin-top: 15px;
}

/* Invoice Items */
.invoice-items {
    border: 1px solid var(--light-gray);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.invoice-item {
    display: grid;
    grid-template-columns: 2fr 100px 120px 120px 40px;
    gap: 10px;
    padding: 15px;
    border-bottom: 1px solid var(--light-gray);
    align-items: center;
}

.invoice-item:last-child {
    border-bottom: none;
}

.invoice-item-header {
    background-color: var(--background-color);
    font-weight: 600;
    font-size: 14px;
}

.invoice-item input,
.invoice-item textarea {
    border: 1px solid var(--medium-gray);
    border-radius: 4px;
    padding: 8px;
    font-size: 14px;
}

.invoice-item textarea {
    resize: vertical;
    min-height: 60px;
}

.remove-item-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px;
    cursor: pointer;
    font-size: 12px;
    transition: var(--transition);
}

.remove-item-btn:hover {
    background: #c82333;
}

/* Invoice Totals */
.invoice-totals {
    background-color: var(--background-color);
    padding: 20px;
    border-radius: var(--border-radius);
    max-width: 300px;
    margin-left: auto;
}

.totals-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.totals-row:last-child {
    margin-bottom: 0;
}

.totals-row label {
    font-weight: 500;
}

.totals-row input {
    width: 80px;
    padding: 4px 8px;
    border: 1px solid var(--medium-gray);
    border-radius: 4px;
}

.total-row {
    border-top: 2px solid var(--primary-color);
    padding-top: 10px;
    margin-top: 10px;
    font-weight: bold;
    font-size: 16px;
}