<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Manager - Login</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="form-container" id="login-signup-container">
            <div class="form-header">
                <h1>Task Manager</h1>
                <p>Manage your tasks efficiently</p>
            </div>
            
            <div class="tabs">
                <button class="tab active" data-tab="login">Login</button>
                <button class="tab" data-tab="signup">Sign Up</button>
            </div>
            
            <div class="form-content">
                <form id="login-form" class="active-form">
                    <div class="form-group">
                        <label for="login-email"><i class="fas fa-envelope"></i></label>
                        <input type="email" id="login-email" placeholder="Email" required>
                    </div>
                    <div class="form-group">
                        <label for="login-password"><i class="fas fa-lock"></i></label>
                        <input type="password" id="login-password" placeholder="Password" required>
                    </div>
                    <button type="submit" class="btn-primary">Login</button>
                </form>
                
                <form id="signup-form">
                    <div class="form-group">
                        <label for="signup-name"><i class="fas fa-user"></i></label>
                        <input type="text" id="signup-name" placeholder="Full Name" required>
                    </div>
                    <div class="form-group">
                        <label for="signup-email"><i class="fas fa-envelope"></i></label>
                        <input type="email" id="signup-email" placeholder="Email" required>
                    </div>
                    <div class="form-group">
                        <label for="signup-password"><i class="fas fa-lock"></i></label>
                        <input type="password" id="signup-password" placeholder="Password" required>
                    </div>
                    <div class="form-group">
                        <label for="signup-confirm-password"><i class="fas fa-lock"></i></label>
                        <input type="password" id="signup-confirm-password" placeholder="Confirm Password" required>
                    </div>
                    <button type="submit" class="btn-primary">Sign Up</button>
                </form>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
</body>
</html>