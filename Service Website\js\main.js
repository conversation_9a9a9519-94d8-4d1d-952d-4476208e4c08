document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const nav = document.querySelector('nav');
    
    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', function() {
            nav.classList.toggle('active');
        });
    }
    
    // Quote modal functionality
    const quoteModal = document.getElementById('quote-modal');
    const graphicDesignQuoteBtn = document.getElementById('graphic-design-quote-btn');
    const digitalMarketingQuoteBtn = document.getElementById('digital-marketing-quote-btn');
    const closeModal = document.querySelector('.close-modal');
    const serviceType = document.getElementById('service-type');
    
    // Function to open modal
    function openModal(service) {
        if (quoteModal) {
            quoteModal.style.display = 'block';
            if (serviceType) {
                serviceType.value = service;
            }
        }
    }
    
    // Function to close modal
    function closeModalFunc() {
        if (quoteModal) {
            quoteModal.style.display = 'none';
        }
    }
    
    // Event listeners for quote buttons
    if (graphicDesignQuoteBtn) {
        graphicDesignQuoteBtn.addEventListener('click', function() {
            openModal('Graphic Design');
        });
    }
    
    if (digitalMarketingQuoteBtn) {
        digitalMarketingQuoteBtn.addEventListener('click', function() {
            openModal('Digital Marketing');
        });
    }
    
    // Close modal when clicking the close button
    if (closeModal) {
        closeModal.addEventListener('click', closeModalFunc);
    }
    
    // Close modal when clicking outside the modal content
    window.addEventListener('click', function(event) {
        if (event.target === quoteModal) {
            closeModalFunc();
        }
    });
    
    // Quote form submission
    const quoteForm = document.getElementById('quote-form');
    if (quoteForm) {
        quoteForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form values
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const address = document.getElementById('address').value;
            const date = document.getElementById('date').value;
            const time = document.getElementById('time').value;
            const message = document.getElementById('message').value;
            const service = document.getElementById('service-type').value;
            
            // Here you would typically send this data to your server
            // For demonstration, we'll just show an alert
            alert(`Thank you for your quote request for ${service}!\n\nWe have received your information and will contact you shortly to discuss your project.`);
            
            // Reset form and close modal
            quoteForm.reset();
            closeModalFunc();
        });
    }
    
    // Contact form submission
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form values
            const name = document.getElementById('contact-name').value;
            const email = document.getElementById('contact-email').value;
            const phone = document.getElementById('contact-phone').value;
            const subject = document.getElementById('contact-subject').value;
            const message = document.getElementById('contact-message').value;
            
            // Here you would typically send this data to your server
            // For demonstration, we'll just show an alert
            alert('Thank you for your message!\n\nWe have received your inquiry and will get back to you as soon as possible.');
            
            // Reset form
            contactForm.reset();
        });
    }
    
    // Payment section toggle
    const webDevPaymentBtn = document.getElementById('web-dev-payment-btn');
    const seoPaymentBtn = document.getElementById('seo-payment-btn');
    const webDevPaymentSection = document.getElementById('web-dev-payment');
    const seoPaymentSection = document.getElementById('seo-payment');
    
    if (webDevPaymentBtn && webDevPaymentSection) {
        webDevPaymentBtn.addEventListener('click', function() {
            webDevPaymentSection.style.display = 'block';
            webDevPaymentBtn.style.display = 'none';
        });
    }
    
    if (seoPaymentBtn && seoPaymentSection) {
        seoPaymentBtn.addEventListener('click', function() {
            seoPaymentSection.style.display = 'block';
            seoPaymentBtn.style.display = 'none';
        });
    }
});