// Invoice Management JavaScript

// DOM Elements
let invoicesList = null;
let createInvoiceBtn = null;
let filterButtons = null;
let currentFilter = 'all';

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    invoicesList = document.getElementById('invoices-list');
    createInvoiceBtn = document.getElementById('create-invoice-btn');
    filterButtons = document.querySelectorAll('#invoices-filter .tab');
    
    if (createInvoiceBtn) {
        createInvoiceBtn.addEventListener('click', showCreateInvoiceForm);
    }
    
    // Add filter event listeners
    filterButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            const filter = e.target.getAttribute('data-filter');
            setActiveFilter(filter);
            loadInvoices(filter);
        });
    });
    
    // Load invoices on page load
    loadInvoices();
    loadDashboardStats();
});

// Set active filter button
function setActiveFilter(filter) {
    filterButtons.forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
    currentFilter = filter;
}

// Load invoices from Supabase
async function loadInvoices(filter = 'all') {
    try {
        let query = supabaseClient
            .from(TABLES.INVOICES)
            .select(`
                *,
                customers (
                    name,
                    email
                )
            `)
            .order('created_at', { ascending: false });
        
        // Apply filter
        if (filter !== 'all') {
            query = query.eq('status', filter);
        }
        
        const { data: invoices, error } = await query;
        
        if (error) {
            console.error('Error loading invoices:', error);
            showError('Failed to load invoices');
            return;
        }
        
        displayInvoices(invoices);
        
    } catch (error) {
        console.error('Error loading invoices:', error);
        showError('Failed to load invoices');
    }
}

// Display invoices in the table
function displayInvoices(invoices) {
    const noInvoicesMessage = document.getElementById('no-invoices-message');
    
    if (!invoices || invoices.length === 0) {
        invoicesList.innerHTML = '';
        noInvoicesMessage.style.display = 'block';
        return;
    }
    
    noInvoicesMessage.style.display = 'none';
    
    invoicesList.innerHTML = invoices.map(invoice => `
        <tr>
            <td><strong>${invoice.invoice_number}</strong></td>
            <td>${invoice.customers?.name || 'N/A'}</td>
            <td>${formatDate(invoice.invoice_date)}</td>
            <td>${invoice.due_date ? formatDate(invoice.due_date) : 'N/A'}</td>
            <td>$${parseFloat(invoice.total_amount || 0).toFixed(2)}</td>
            <td><span class="status-badge status-${invoice.status}">${capitalizeFirst(invoice.status)}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="btn-icon" onclick="viewInvoice('${invoice.id}')" title="View">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon" onclick="editInvoice('${invoice.id}')" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon" onclick="duplicateInvoice('${invoice.id}')" title="Duplicate">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="btn-icon btn-danger" onclick="deleteInvoice('${invoice.id}')" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// Load dashboard statistics
async function loadDashboardStats() {
    try {
        const { data: invoices, error } = await supabaseClient
            .from(TABLES.INVOICES)
            .select('status, total_amount');
        
        if (error) {
            console.error('Error loading stats:', error);
            return;
        }
        
        const stats = {
            total: invoices.length,
            pending: invoices.filter(inv => inv.status === 'sent' || inv.status === 'draft').length,
            paid: invoices.filter(inv => inv.status === 'paid').length,
            revenue: invoices.filter(inv => inv.status === 'paid')
                .reduce((sum, inv) => sum + parseFloat(inv.total_amount || 0), 0)
        };
        
        // Update DOM
        document.getElementById('total-invoices').textContent = stats.total;
        document.getElementById('pending-invoices').textContent = stats.pending;
        document.getElementById('paid-invoices').textContent = stats.paid;
        document.getElementById('total-revenue').textContent = `$${stats.revenue.toFixed(2)}`;
        
    } catch (error) {
        console.error('Error loading dashboard stats:', error);
    }
}

// Show create invoice form
function showCreateInvoiceForm() {
    const modal = document.getElementById('invoice-modal');
    const form = document.getElementById('invoice-form');

    // Reset form
    form.reset();
    document.getElementById('modal-title').textContent = 'Create New Invoice';

    // Set default date to today
    document.getElementById('invoice-date').value = new Date().toISOString().split('T')[0];

    // Load customers for dropdown
    loadCustomersDropdown();

    // Clear invoice items and add one default item
    document.getElementById('invoice-items').innerHTML = '';
    addInvoiceItem();

    // Show modal
    modal.style.display = 'block';

    // Add form submit handler
    form.onsubmit = handleInvoiceSubmit;
}

// Invoice action functions (placeholders)
function viewInvoice(id) {
    alert(`View invoice ${id} - will be implemented soon!`);
}

function editInvoice(id) {
    alert(`Edit invoice ${id} - will be implemented soon!`);
}

function duplicateInvoice(id) {
    alert(`Duplicate invoice ${id} - will be implemented soon!`);
}

async function deleteInvoice(id) {
    if (!confirm('Are you sure you want to delete this invoice?')) {
        return;
    }
    
    try {
        const { error } = await supabaseClient
            .from(TABLES.INVOICES)
            .delete()
            .eq('id', id);
        
        if (error) {
            console.error('Error deleting invoice:', error);
            showError('Failed to delete invoice');
            return;
        }
        
        // Reload invoices and stats
        loadInvoices(currentFilter);
        loadDashboardStats();
        showSuccess('Invoice deleted successfully');
        
    } catch (error) {
        console.error('Error deleting invoice:', error);
        showError('Failed to delete invoice');
    }
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString();
}

function capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}

function showError(message) {
    // Simple error display - can be enhanced with a proper notification system
    alert('Error: ' + message);
}

function showSuccess(message) {
    // Simple success display - can be enhanced with a proper notification system
    alert('Success: ' + message);
}

// Close invoice modal
function closeInvoiceModal() {
    document.getElementById('invoice-modal').style.display = 'none';
}

// Load customers for dropdown
async function loadCustomersDropdown() {
    try {
        const { data: customers, error } = await supabaseClient
            .from(TABLES.CUSTOMERS)
            .select('*')
            .order('name');

        if (error) {
            console.error('Error loading customers:', error);
            return;
        }

        const select = document.getElementById('customer-select');
        select.innerHTML = '<option value="">Select existing customer or create new</option>';

        customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = customer.name;
            option.dataset.customer = JSON.stringify(customer);
            select.appendChild(option);
        });

    } catch (error) {
        console.error('Error loading customers:', error);
    }
}

// Handle customer selection
function handleCustomerSelect() {
    const select = document.getElementById('customer-select');
    const customerForm = document.getElementById('customer-form');

    if (select.value) {
        // Existing customer selected
        const customer = JSON.parse(select.options[select.selectedIndex].dataset.customer);
        populateCustomerForm(customer);
        customerForm.style.display = 'block';

        // Disable form fields for existing customer
        const inputs = customerForm.querySelectorAll('input, textarea');
        inputs.forEach(input => input.disabled = true);
    } else {
        // New customer or no selection
        clearCustomerForm();
        customerForm.style.display = 'none';
    }
}

// Toggle new customer form
function toggleNewCustomerForm() {
    const customerForm = document.getElementById('customer-form');
    const select = document.getElementById('customer-select');

    // Clear selection and show form
    select.value = '';
    clearCustomerForm();
    customerForm.style.display = 'block';

    // Enable all form fields
    const inputs = customerForm.querySelectorAll('input, textarea');
    inputs.forEach(input => input.disabled = false);
}

// Populate customer form with existing customer data
function populateCustomerForm(customer) {
    document.getElementById('customer-name').value = customer.name || '';
    document.getElementById('customer-email').value = customer.email || '';
    document.getElementById('customer-phone').value = customer.phone || '';
    document.getElementById('customer-address').value = customer.address || '';
    document.getElementById('customer-city').value = customer.city || '';
    document.getElementById('customer-state').value = customer.state || '';
    document.getElementById('customer-zip').value = customer.zip_code || '';
}

// Clear customer form
function clearCustomerForm() {
    document.getElementById('customer-name').value = '';
    document.getElementById('customer-email').value = '';
    document.getElementById('customer-phone').value = '';
    document.getElementById('customer-address').value = '';
    document.getElementById('customer-city').value = '';
    document.getElementById('customer-state').value = '';
    document.getElementById('customer-zip').value = '';
}

// Add invoice item
function addInvoiceItem() {
    const container = document.getElementById('invoice-items');
    const itemCount = container.children.length;

    // Add header if this is the first item
    if (itemCount === 0) {
        const header = document.createElement('div');
        header.className = 'invoice-item invoice-item-header';
        header.innerHTML = `
            <div>Description</div>
            <div>Quantity</div>
            <div>Unit Price</div>
            <div>Total</div>
            <div></div>
        `;
        container.appendChild(header);
    }

    const itemDiv = document.createElement('div');
    itemDiv.className = 'invoice-item';
    itemDiv.innerHTML = `
        <textarea placeholder="Item description" onchange="calculateTotals()" required></textarea>
        <input type="number" min="0" step="0.01" value="1" onchange="calculateTotals()" required>
        <input type="number" min="0" step="0.01" placeholder="0.00" onchange="calculateTotals()" required>
        <span class="item-total">$0.00</span>
        <button type="button" class="remove-item-btn" onclick="removeInvoiceItem(this)">×</button>
    `;

    container.appendChild(itemDiv);
    calculateTotals();
}

// Remove invoice item
function removeInvoiceItem(button) {
    const container = document.getElementById('invoice-items');
    const item = button.closest('.invoice-item');

    item.remove();

    // Remove header if no items left
    if (container.children.length === 1) {
        container.innerHTML = '';
    }

    calculateTotals();
}

// Calculate invoice totals
function calculateTotals() {
    const items = document.querySelectorAll('#invoice-items .invoice-item:not(.invoice-item-header)');
    let subtotal = 0;

    items.forEach(item => {
        const quantity = parseFloat(item.children[1].value) || 0;
        const unitPrice = parseFloat(item.children[2].value) || 0;
        const total = quantity * unitPrice;

        item.children[3].textContent = `$${total.toFixed(2)}`;
        subtotal += total;
    });

    const taxRate = parseFloat(document.getElementById('tax-rate').value) || 0;
    const taxAmount = subtotal * (taxRate / 100);
    const totalAmount = subtotal + taxAmount;

    document.getElementById('subtotal-display').textContent = `$${subtotal.toFixed(2)}`;
    document.getElementById('tax-display').textContent = `$${taxAmount.toFixed(2)}`;
    document.getElementById('total-display').textContent = `$${totalAmount.toFixed(2)}`;
}

// Handle invoice form submission
async function handleInvoiceSubmit(e) {
    e.preventDefault();

    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
    submitBtn.disabled = true;

    try {
        // Get form data
        const formData = getInvoiceFormData();

        // Validate form data
        if (!validateInvoiceForm(formData)) {
            return;
        }

        // Save customer (if new)
        let customerId = document.getElementById('customer-select').value;
        if (!customerId) {
            const customerData = {
                name: formData.customer.name,
                email: formData.customer.email,
                phone: formData.customer.phone,
                address: formData.customer.address,
                city: formData.customer.city,
                state: formData.customer.state,
                zip_code: formData.customer.zip,
                user_id: (await supabaseClient.auth.getUser()).data.user.id
            };

            const { data: customer, error: customerError } = await supabaseClient
                .from(TABLES.CUSTOMERS)
                .insert(customerData)
                .select()
                .single();

            if (customerError) {
                throw customerError;
            }

            customerId = customer.id;
        }

        // Generate invoice number
        const { data: invoiceNumberData, error: invoiceNumberError } = await supabaseClient
            .rpc('generate_invoice_number');

        if (invoiceNumberError) {
            throw invoiceNumberError;
        }

        // Save invoice
        const invoiceData = {
            customer_id: customerId,
            invoice_number: invoiceNumberData,
            invoice_date: formData.invoice.date,
            due_date: formData.invoice.dueDate || null,
            status: formData.invoice.status,
            subtotal: formData.totals.subtotal,
            tax_rate: formData.totals.taxRate,
            tax_amount: formData.totals.taxAmount,
            total_amount: formData.totals.total,
            notes: formData.invoice.notes || null,
            terms: formData.invoice.terms || null,
            user_id: (await supabaseClient.auth.getUser()).data.user.id
        };

        const { data: invoice, error: invoiceError } = await supabaseClient
            .from(TABLES.INVOICES)
            .insert(invoiceData)
            .select()
            .single();

        if (invoiceError) {
            throw invoiceError;
        }

        // Save invoice items
        const itemsData = formData.items.map(item => ({
            invoice_id: invoice.id,
            description: item.description,
            quantity: item.quantity,
            unit_price: item.unitPrice
        }));

        const { error: itemsError } = await supabaseClient
            .from(TABLES.INVOICE_ITEMS)
            .insert(itemsData);

        if (itemsError) {
            throw itemsError;
        }

        // Success
        showSuccess('Invoice created successfully!');
        closeInvoiceModal();
        loadInvoices(currentFilter);
        loadDashboardStats();

    } catch (error) {
        console.error('Error saving invoice:', error);
        showError('Failed to save invoice: ' + error.message);
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

// Get form data
function getInvoiceFormData() {
    const items = [];
    const itemElements = document.querySelectorAll('#invoice-items .invoice-item:not(.invoice-item-header)');

    itemElements.forEach(item => {
        const description = item.children[0].value.trim();
        const quantity = parseFloat(item.children[1].value) || 0;
        const unitPrice = parseFloat(item.children[2].value) || 0;

        if (description && quantity > 0 && unitPrice >= 0) {
            items.push({
                description,
                quantity,
                unitPrice
            });
        }
    });

    const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
    const taxRate = parseFloat(document.getElementById('tax-rate').value) || 0;
    const taxAmount = subtotal * (taxRate / 100);
    const total = subtotal + taxAmount;

    return {
        customer: {
            name: document.getElementById('customer-name').value.trim(),
            email: document.getElementById('customer-email').value.trim(),
            phone: document.getElementById('customer-phone').value.trim(),
            address: document.getElementById('customer-address').value.trim(),
            city: document.getElementById('customer-city').value.trim(),
            state: document.getElementById('customer-state').value.trim(),
            zip: document.getElementById('customer-zip').value.trim()
        },
        invoice: {
            date: document.getElementById('invoice-date').value,
            dueDate: document.getElementById('due-date').value,
            status: document.getElementById('invoice-status').value,
            notes: document.getElementById('invoice-notes').value.trim(),
            terms: document.getElementById('invoice-terms').value.trim()
        },
        items,
        totals: {
            subtotal,
            taxRate,
            taxAmount,
            total
        }
    };
}

// Validate invoice form
function validateInvoiceForm(formData) {
    if (!formData.customer.name) {
        showError('Customer name is required');
        return false;
    }

    if (!formData.invoice.date) {
        showError('Invoice date is required');
        return false;
    }

    if (formData.items.length === 0) {
        showError('At least one invoice item is required');
        return false;
    }

    return true;
}
