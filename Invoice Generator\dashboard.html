<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Generator - Dashboard</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="dashboard-container">
            <div class="dashboard-header">
                <h1><i class="fas fa-file-invoice"></i> Invoice Generator</h1>
                <div class="user-info">
                    <span id="user-name">Welcome, User</span>
                    <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</button>
                </div>
            </div>
            
            <div class="dashboard-content">
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-invoices">0</h3>
                            <p>Total Invoices</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="pending-invoices">0</h3>
                            <p>Pending</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="paid-invoices">0</h3>
                            <p>Paid</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-revenue">$0</h3>
                            <p>Total Revenue</p>
                        </div>
                    </div>
                </div>

                <div class="invoices-container">
                    <div class="invoices-header">
                        <h2>Your Invoices</h2>
                        <button id="create-invoice-btn" class="btn-primary">
                            <i class="fas fa-plus"></i> Create New Invoice
                        </button>
                    </div>

                    <div id="invoices-filter" class="mb-20">
                        <button class="tab active" data-filter="all">All</button>
                        <button class="tab" data-filter="draft">Draft</button>
                        <button class="tab" data-filter="sent">Sent</button>
                        <button class="tab" data-filter="paid">Paid</button>
                        <button class="tab" data-filter="overdue">Overdue</button>
                    </div>

                    <div id="invoices-table-container">
                        <table id="invoices-table" class="invoices-table">
                            <thead>
                                <tr>
                                    <th>Invoice #</th>
                                    <th>Customer</th>
                                    <th>Date</th>
                                    <th>Due Date</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="invoices-list">
                                <!-- Invoices will be added here dynamically -->
                            </tbody>
                        </table>

                        <div id="no-invoices-message" class="text-center mt-20" style="display: none;">
                            <div class="empty-state">
                                <i class="fas fa-file-invoice fa-3x"></i>
                                <h3>No invoices yet</h3>
                                <p>Create your first invoice to get started!</p>
                                <button class="btn-primary" onclick="document.getElementById('create-invoice-btn').click()">
                                    <i class="fas fa-plus"></i> Create Invoice
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Creation Modal -->
    <div id="invoice-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">Create New Invoice</h2>
                <button class="modal-close" onclick="closeInvoiceModal()">&times;</button>
            </div>

            <form id="invoice-form" class="modal-body">
                <!-- Customer Information -->
                <div class="form-section">
                    <h3>Customer Information</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="customer-select">Select Customer</label>
                            <select id="customer-select" onchange="handleCustomerSelect()">
                                <option value="">Select existing customer or create new</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <button type="button" id="new-customer-btn" class="btn-secondary" onclick="toggleNewCustomerForm()">
                                <i class="fas fa-plus"></i> New Customer
                            </button>
                        </div>
                    </div>

                    <div id="customer-form" class="customer-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="customer-name">Customer Name *</label>
                                <input type="text" id="customer-name" required>
                            </div>
                            <div class="form-group">
                                <label for="customer-email">Email</label>
                                <input type="email" id="customer-email">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="customer-phone">Phone</label>
                                <input type="tel" id="customer-phone">
                            </div>
                            <div class="form-group">
                                <label for="customer-address">Address</label>
                                <textarea id="customer-address" rows="2"></textarea>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="customer-city">City</label>
                                <input type="text" id="customer-city">
                            </div>
                            <div class="form-group">
                                <label for="customer-state">State</label>
                                <input type="text" id="customer-state">
                            </div>
                            <div class="form-group">
                                <label for="customer-zip">ZIP Code</label>
                                <input type="text" id="customer-zip">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Details -->
                <div class="form-section">
                    <h3>Invoice Details</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="invoice-date">Invoice Date *</label>
                            <input type="date" id="invoice-date" required>
                        </div>
                        <div class="form-group">
                            <label for="due-date">Due Date</label>
                            <input type="date" id="due-date">
                        </div>
                        <div class="form-group">
                            <label for="invoice-status">Status</label>
                            <select id="invoice-status">
                                <option value="draft">Draft</option>
                                <option value="sent">Sent</option>
                                <option value="paid">Paid</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Invoice Items -->
                <div class="form-section">
                    <div class="section-header">
                        <h3>Invoice Items</h3>
                        <button type="button" class="btn-secondary" onclick="addInvoiceItem()">
                            <i class="fas fa-plus"></i> Add Item
                        </button>
                    </div>

                    <div id="invoice-items" class="invoice-items">
                        <!-- Items will be added dynamically -->
                    </div>
                </div>

                <!-- Invoice Totals -->
                <div class="form-section">
                    <div class="invoice-totals">
                        <div class="totals-row">
                            <label>Subtotal:</label>
                            <span id="subtotal-display">$0.00</span>
                        </div>
                        <div class="totals-row">
                            <label for="tax-rate">Tax Rate (%):</label>
                            <input type="number" id="tax-rate" min="0" max="100" step="0.01" value="0" onchange="calculateTotals()">
                        </div>
                        <div class="totals-row">
                            <label>Tax Amount:</label>
                            <span id="tax-display">$0.00</span>
                        </div>
                        <div class="totals-row total-row">
                            <label>Total:</label>
                            <span id="total-display">$0.00</span>
                        </div>
                    </div>
                </div>

                <!-- Notes and Terms -->
                <div class="form-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="invoice-notes">Notes</label>
                            <textarea id="invoice-notes" rows="3" placeholder="Additional notes for this invoice"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="invoice-terms">Terms & Conditions</label>
                            <textarea id="invoice-terms" rows="3" placeholder="Payment terms and conditions"></textarea>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn-secondary" onclick="closeInvoiceModal()">Cancel</button>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save"></i> Save Invoice
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/supabase-config.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/invoices.js"></script>
</body>
</html>