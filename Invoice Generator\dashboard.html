<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Manager - Dashboard</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="dashboard-container">
            <div class="dashboard-header">
                <h1>Task Manager</h1>
                <div class="user-info">
                    <span id="user-name">Welcome, User</span>
                    <button id="logout-btn" class="logout-btn">Logout</button>
                </div>
            </div>
            
            <div class="dashboard-content">
                <div class="add-task-form">
                    <h2 class="mb-20">Add New Task</h2>
                    <form id="task-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="task-title">Task Title</label>
                                <input type="text" id="task-title" placeholder="Enter task title" required>
                            </div>
                            <div class="form-group">
                                <label for="task-deadline">Deadline</label>
                                <input type="date" id="task-deadline" required>
                            </div>
                        </div>
                        <button type="submit" class="btn-primary">Add Task</button>
                    </form>
                </div>
                
                <div class="tasks-container">
                    <h2 class="mb-20">Your Tasks</h2>
                    <div id="tasks-filter" class="mb-20">
                        <button class="tab active" data-filter="all">All</button>
                        <button class="tab" data-filter="active">Active</button>
                        <button class="tab" data-filter="completed">Completed</button>
                    </div>
                    
                    <ul id="task-list" class="task-list">
                        <!-- Tasks will be added here dynamically -->
                        <div id="no-tasks-message" class="text-center mt-20">
                            <p>You don't have any tasks yet. Add a task to get started!</p>
                        </div>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/tasks.js"></script>
</body>
</html>