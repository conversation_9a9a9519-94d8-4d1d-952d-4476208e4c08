document.addEventListener('DOMContentLoaded', function() {
    // Initialize Stripe with your publishable key
    // Replace 'your_publishable_key' with your actual Stripe publishable key when going live
    const stripe = Stripe('pk_test_TYooMQauvdEDq54NiTphI7jx');
    
    // Create elements instances for both payment forms
    const webDevElements = stripe.elements();
    const seoElements = stripe.elements();
    
    // Custom styling for the card elements
    const style = {
        base: {
            color: '#32325d',
            fontFamily: '"Segoe UI", "Helvetica Neue", Helvetica, sans-serif',
            fontSmoothing: 'antialiased',
            fontSize: '16px',
            '::placeholder': {
                color: '#aab7c4'
            }
        },
        invalid: {
            color: '#dc3545',
            iconColor: '#dc3545'
        }
    };
    
    // Create card elements for both forms
    const webDevCardElement = webDevElements.create('card', {style: style});
    const seoCardElement = seoElements.create('card', {style: style});
    
    // Mount the card elements to the DOM
    const webDevCardMount = document.getElementById('web-dev-card-element');
    const seoCardMount = document.getElementById('seo-card-element');
    
    if (webDevCardMount) {
        webDevCardElement.mount('#web-dev-card-element');
    }
    
    if (seoCardMount) {
        seoCardElement.mount('#seo-card-element');
    }
    
    // Handle real-time validation errors for Web Development form
    webDevCardElement.addEventListener('change', function(event) {
        const displayError = document.getElementById('web-dev-card-errors');
        if (event.error) {
            displayError.textContent = event.error.message;
        } else {
            displayError.textContent = '';
        }
    });
    
    // Handle real-time validation errors for SEO form
    seoCardElement.addEventListener('change', function(event) {
        const displayError = document.getElementById('seo-card-errors');
        if (event.error) {
            displayError.textContent = event.error.message;
        } else {
            displayError.textContent = '';
        }
    });
    
    // Handle form submission for Web Development payment
    const webDevForm = document.getElementById('web-dev-payment-form');
    if (webDevForm) {
        webDevForm.addEventListener('submit', function(event) {
            event.preventDefault();
            
            // Disable the submit button to prevent multiple submissions
            document.querySelector('#web-dev-payment-form button').disabled = true;
            
            // Get customer information
            const name = document.getElementById('web-dev-name').value;
            const email = document.getElementById('web-dev-email').value;
            
            // In a real implementation, you would create a payment intent on your server
            // and return a client secret to complete the payment on the client side
            // For demonstration purposes, we'll simulate a successful payment
            
            // Simulate payment processing
            setTimeout(function() {
                // Show success message
                alert('Payment successful! Thank you for purchasing our Web Development service. We will contact you shortly to begin the project.');
                
                // Reset form and hide payment section
                webDevForm.reset();
                document.getElementById('web-dev-payment').style.display = 'none';
                document.getElementById('web-dev-payment-btn').style.display = 'inline-block';
                document.querySelector('#web-dev-payment-form button').disabled = false;
            }, 2000);
            
            /* In a real implementation, you would use Stripe.js to create a payment method and confirm the payment
            
            stripe.confirmCardPayment(clientSecret, {
                payment_method: {
                    card: webDevCardElement,
                    billing_details: {
                        name: name,
                        email: email
                    }
                }
            }).then(function(result) {
                if (result.error) {
                    // Show error message
                    const errorElement = document.getElementById('web-dev-card-errors');
                    errorElement.textContent = result.error.message;
                    document.querySelector('#web-dev-payment-form button').disabled = false;
                } else {
                    if (result.paymentIntent.status === 'succeeded') {
                        // Payment successful
                        alert('Payment successful! Thank you for purchasing our Web Development service.');
                        webDevForm.reset();
                        document.getElementById('web-dev-payment').style.display = 'none';
                        document.getElementById('web-dev-payment-btn').style.display = 'inline-block';
                    }
                }
            });
            */
        });
    }
    
    // Handle form submission for SEO payment
    const seoForm = document.getElementById('seo-payment-form');
    if (seoForm) {
        seoForm.addEventListener('submit', function(event) {
            event.preventDefault();
            
            // Disable the submit button to prevent multiple submissions
            document.querySelector('#seo-payment-form button').disabled = true;
            
            // Get customer information
            const name = document.getElementById('seo-name').value;
            const email = document.getElementById('seo-email').value;
            
            // In a real implementation, you would create a payment intent on your server
            // and return a client secret to complete the payment on the client side
            // For demonstration purposes, we'll simulate a successful payment
            
            // Simulate payment processing
            setTimeout(function() {
                // Show success message
                alert('Payment successful! Thank you for purchasing our SEO Optimization service. We will contact you shortly to begin the project.');
                
                // Reset form and hide payment section
                seoForm.reset();
                document.getElementById('seo-payment').style.display = 'none';
                document.getElementById('seo-payment-btn').style.display = 'inline-block';
                document.querySelector('#seo-payment-form button').disabled = false;
            }, 2000);
            
            /* In a real implementation, you would use Stripe.js to create a payment method and confirm the payment
            
            stripe.confirmCardPayment(clientSecret, {
                payment_method: {
                    card: seoCardElement,
                    billing_details: {
                        name: name,
                        email: email
                    }
                }
            }).then(function(result) {
                if (result.error) {
                    // Show error message
                    const errorElement = document.getElementById('seo-card-errors');
                    errorElement.textContent = result.error.message;
                    document.querySelector('#seo-payment-form button').disabled = false;
                } else {
                    if (result.paymentIntent.status === 'succeeded') {
                        // Payment successful
                        alert('Payment successful! Thank you for purchasing our SEO Optimization service.');
                        seoForm.reset();
                        document.getElementById('seo-payment').style.display = 'none';
                        document.getElementById('seo-payment-btn').style.display = 'inline-block';
                    }
                }
            });
            */
        });
    }
});