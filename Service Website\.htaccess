# Improve security and performance

# Enable GZIP compression
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
</IfModule>

# Set browser caching
<IfModule mod_expires.c>
  ExpiresActive On
  ExpiresByType image/jpg "access plus 1 year"
  ExpiresByType image/jpeg "access plus 1 year"
  ExpiresByType image/gif "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
  ExpiresByType image/svg+xml "access plus 1 year"
  ExpiresByType text/css "access plus 1 month"
  ExpiresByType application/pdf "access plus 1 month"
  ExpiresByType text/javascript "access plus 1 month"
  ExpiresByType application/javascript "access plus 1 month"
  ExpiresByType application/x-javascript "access plus 1 month"
  ExpiresByType application/x-shockwave-flash "access plus 1 month"
  ExpiresByType image/x-icon "access plus 1 year"
  ExpiresDefault "access plus 2 days"
</IfModule>

# Protect against XSS attacks
<IfModule mod_headers.c>
  Header set X-XSS-Protection "1; mode=block"
  Header set X-Content-Type-Options "nosniff"
  Header set X-Frame-Options "SAMEORIGIN"
  Header set Referrer-Policy "strict-origin-when-cross-origin"
  Header set Content-Security-Policy "default-src 'self' https://cdnjs.cloudflare.com https://js.stripe.com https://www.google.com; img-src 'self' https://images.unsplash.com data:; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; script-src 'self' 'unsafe-inline' https://js.stripe.com; frame-src https://www.google.com https://js.stripe.com;"
</IfModule>

# Prevent directory listing
Options -Indexes

# Prevent access to hidden files
<FilesMatch "^\.">  
  Order allow,deny
  Deny from all
</FilesMatch>

# Custom error pages
ErrorDocument 404 /404.html
ErrorDocument 500 /500.html

# Redirect www to non-www (or vice versa)
# Uncomment one of the following blocks when deploying to production

# www to non-www
#<IfModule mod_rewrite.c>
#  RewriteEngine On
#  RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
#  RewriteRule ^(.*)$ https://%1/$1 [R=301,L]
#</IfModule>

# non-www to www
#<IfModule mod_rewrite.c>
#  RewriteEngine On
#  RewriteCond %{HTTP_HOST} !^www\. [NC]
#  RewriteRule ^(.*)$ https://www.%{HTTP_HOST}/$1 [R=301,L]
#</IfModule>

# Force HTTPS
#<IfModule mod_rewrite.c>
#  RewriteEngine On
#  RewriteCond %{HTTPS} off
#  RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
#</IfModule>