/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

a {
    text-decoration: none;
    color: #333;
}

ul {
    list-style: none;
}

.btn {
    display: inline-block;
    background-color: #4a6fdc;
    color: #fff;
    padding: 10px 20px;
    border-radius: 5px;
    transition: background-color 0.3s ease;
    font-weight: 500;
    border: none;
    cursor: pointer;
}

.btn:hover {
    background-color: #3a5bb9;
}

.buy-btn {
    background-color: #4a6fdc;
}

.buy-btn:hover {
    background-color: #3a5bb9;
}

.quote-btn {
    background-color: #28a745;
}

.quote-btn:hover {
    background-color: #218838;
}

.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-header h2 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    color: #333;
}

.section-header p {
    color: #666;
    font-size: 1.1rem;
}

/* Header Styles */
header {
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #4a6fdc;
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 30px;
}

nav ul li a {
    font-weight: 500;
    position: relative;
    padding-bottom: 5px;
}

nav ul li a:hover,
nav ul li a.active {
    color: #4a6fdc;
}

nav ul li a::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 0;
    height: 2px;
    background-color: #4a6fdc;
    transition: width 0.3s ease;
}

nav ul li a:hover::after,
nav ul li a.active::after {
    width: 100%;
}

.mobile-menu-btn {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('https://images.unsplash.com/photo-1521737711867-e3b97375f902?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80');
    background-size: cover;
    background-position: center;
    color: #fff;
    padding: 100px 0;
    text-align: center;
}

.hero-content h2 {
    font-size: 3rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

/* Services Section */
.services {
    padding: 80px 0;
    background-color: #fff;
}

.service-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.service-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 30px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.service-card .icon {
    font-size: 2.5rem;
    color: #4a6fdc;
    margin-bottom: 20px;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #333;
}

.service-card p {
    color: #666;
    margin-bottom: 20px;
}

/* About Section */
.about {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.about-content p {
    margin-bottom: 20px;
    font-size: 1.1rem;
    color: #555;
}

/* Footer */
footer {
    background-color: #333;
    color: #fff;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo h2 {
    font-size: 1.8rem;
    margin-bottom: 10px;
    color: #fff;
}

.footer-logo p {
    color: #aaa;
}

.footer-links h3,
.footer-contact h3 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    color: #fff;
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links ul li a {
    color: #aaa;
    transition: color 0.3s ease;
}

.footer-links ul li a:hover {
    color: #fff;
}

.footer-contact p {
    margin-bottom: 10px;
    color: #aaa;
}

.footer-contact i {
    margin-right: 10px;
    color: #4a6fdc;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #444;
    color: #aaa;
}

/* Services Page Styles */
.service-detail {
    padding: 80px 0;
}

.service-item {
    margin-bottom: 60px;
    padding: 40px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.service-item:last-child {
    margin-bottom: 0;
}

.service-item h3 {
    font-size: 2rem;
    margin-bottom: 20px;
    color: #333;
}

.service-item p {
    margin-bottom: 20px;
    color: #666;
    line-height: 1.7;
}

.service-features {
    margin: 30px 0;
}

.service-features h4 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #333;
}

.service-features ul {
    list-style: disc;
    padding-left: 20px;
}

.service-features ul li {
    margin-bottom: 10px;
    color: #666;
}

.service-price {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.service-price h4 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: #333;
}

.service-price p {
    font-size: 2rem;
    font-weight: 700;
    color: #4a6fdc;
    margin-bottom: 0;
}

.service-price span {
    font-size: 1rem;
    color: #666;
    font-weight: normal;
}

/* Contact Page Styles */
.contact-section {
    padding: 80px 0;
}

.contact-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.contact-info {
    background-color: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.contact-info h3 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #333;
}

.contact-info p {
    margin-bottom: 20px;
    color: #666;
}

.contact-info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
}

.contact-info-item i {
    font-size: 1.5rem;
    color: #4a6fdc;
    margin-right: 15px;
    margin-top: 5px;
}

.contact-info-item div h4 {
    font-size: 1.2rem;
    margin-bottom: 5px;
    color: #333;
}

.contact-info-item div p {
    color: #666;
    margin-bottom: 0;
}

.contact-form {
    background-color: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.contact-form h3 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #333;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    color: #333;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    border-color: #4a6fdc;
    outline: none;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Quote Form Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 600px;
    position: relative;
    animation: modalopen 0.3s;
}

@keyframes modalopen {
    from {opacity: 0; transform: translateY(-50px);}
    to {opacity: 1; transform: translateY(0);}
}

.close-modal {
    position: absolute;
    right: 20px;
    top: 15px;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: #333;
}

.modal-header {
    margin-bottom: 20px;
}

.modal-header h3 {
    font-size: 1.8rem;
    color: #333;
}

/* Terms and Conditions Page */
.terms-section {
    padding: 80px 0;
}

.terms-content {
    background-color: #fff;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.terms-content h2 {
    font-size: 2.5rem;
    margin-bottom: 30px;
    color: #333;
}

.terms-content h3 {
    font-size: 1.8rem;
    margin: 30px 0 15px;
    color: #333;
}

.terms-content p {
    margin-bottom: 20px;
    color: #666;
    line-height: 1.7;
}

.terms-content ul {
    list-style: disc;
    padding-left: 20px;
    margin-bottom: 20px;
}

.terms-content ul li {
    margin-bottom: 10px;
    color: #666;
}

/* Payment Section */
.payment-section {
    background-color: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
    margin-top: 30px;
}

.payment-section h4 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: #333;
}

.payment-form {
    max-width: 500px;
}

.card-element {
    background-color: #fff;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #ddd;
    margin-bottom: 20px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .hero-content h2 {
        font-size: 2.5rem;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    header .container {
        flex-direction: column;
        text-align: center;
    }
    
    nav {
        margin-top: 20px;
    }
    
    nav ul {
        flex-direction: column;
        text-align: center;
    }
    
    nav ul li {
        margin: 10px 0;
        margin-left: 0;
    }
    
    .hero-content h2 {
        font-size: 2rem;
    }
    
    .hero-content p {
        font-size: 1rem;
    }
    
    .section-header h2 {
        font-size: 1.8rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .contact-info-item {
        flex-direction: column;
        text-align: center;
    }
    
    .contact-info-item i {
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    .mobile-menu-btn {
        display: block;
        position: absolute;
        top: 20px;
        right: 20px;
    }
    
    nav {
        display: none;
    }
    
    nav.active {
        display: block;
    }
}

@media (max-width: 576px) {
    .hero {
        padding: 60px 0;
    }
    
    .hero-content h2 {
        font-size: 1.8rem;
    }
    
    .section-header h2 {
        font-size: 1.5rem;
    }
    
    .service-item,
    .contact-form,
    .contact-info,
    .terms-content {
        padding: 20px;
    }
}