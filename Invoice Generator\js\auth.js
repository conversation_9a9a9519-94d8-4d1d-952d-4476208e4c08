// DOM Elements
const loginForm = document.getElementById('login-form');
const signupForm = document.getElementById('signup-form');
const loginTab = document.querySelector('.tab[data-tab="login"]');
const signupTab = document.querySelector('.tab[data-tab="signup"]');

// Event Listeners
loginTab.addEventListener('click', () => switchTab('login'));
signupTab.addEventListener('click', () => switchTab('signup'));
loginForm.addEventListener('submit', handleLogin);
signupForm.addEventListener('submit', handleSignup);

// Functions
function switchTab(tab) {
    // Update active tab
    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
    document.querySelector(`.tab[data-tab="${tab}"]`).classList.add('active');
    
    // Show the correct form
    loginForm.style.display = tab === 'login' ? 'block' : 'none';
    signupForm.style.display = tab === 'signup' ? 'block' : 'none';
}

function handleLogin(e) {
    e.preventDefault();
    
    const email = document.getElementById('login-email').value;
    const password = document.getElementById('login-password').value;
    
    // In a real app, you would validate and send this to a server
    // For this demo, we'll use localStorage to simulate authentication
    const users = JSON.parse(localStorage.getItem('users') || '[]');
    const user = users.find(u => u.email === email && u.password === password);
    
    if (user) {
        // Store current user info
        localStorage.setItem('currentUser', JSON.stringify({
            name: user.name,
            email: user.email
        }));
        
        // Redirect to dashboard
        window.location.href = 'dashboard.html';
    } else {
        showError(loginForm, 'Invalid email or password');
    }
}

function handleSignup(e) {
    e.preventDefault();
    
    const name = document.getElementById('signup-name').value;
    const email = document.getElementById('signup-email').value;
    const password = document.getElementById('signup-password').value;
    const confirmPassword = document.getElementById('signup-confirm-password').value;
    
    // Validate passwords match
    if (password !== confirmPassword) {
        showError(signupForm, 'Passwords do not match');
        return;
    }
    
    // In a real app, you would send this to a server
    // For this demo, we'll use localStorage to simulate user registration
    const users = JSON.parse(localStorage.getItem('users') || '[]');
    
    // Check if email already exists
    if (users.some(u => u.email === email)) {
        showError(signupForm, 'Email already registered');
        return;
    }
    
    // Add new user
    users.push({ name, email, password });
    localStorage.setItem('users', JSON.stringify(users));
    
    // Show success message and switch to login
    showSuccess(signupForm, 'Account created successfully! Please log in.');
    setTimeout(() => switchTab('login'), 2000);
}

function showError(form, message) {
    // Remove any existing messages
    removeMessages(form);
    
    // Create and append error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.textContent = message;
    form.appendChild(errorDiv);
}

function showSuccess(form, message) {
    // Remove any existing messages
    removeMessages(form);
    
    // Create and append success message
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.textContent = message;
    form.appendChild(successDiv);
}

function removeMessages(form) {
    const messages = form.querySelectorAll('.error-message, .success-message');
    messages.forEach(msg => msg.remove());
}

// Check if user is already logged in
function checkAuth() {
    const currentUser = localStorage.getItem('currentUser');
    if (currentUser && window.location.pathname.includes('index.html')) {
        window.location.href = 'dashboard.html';
    }
}

// Run auth check when page loads
checkAuth();