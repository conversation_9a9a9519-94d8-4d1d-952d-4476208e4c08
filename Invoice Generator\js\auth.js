// DOM Elements
const loginForm = document.getElementById('login-form');
const signupForm = document.getElementById('signup-form');
const loginTab = document.querySelector('.tab[data-tab="login"]');
const signupTab = document.querySelector('.tab[data-tab="signup"]');
const logoutBtn = document.getElementById('logout-btn');

// Event Listeners
document.addEventListener('DOMContentLoaded', () => {
    if (loginTab) loginTab.addEventListener('click', () => switchTab('login'));
    if (signupTab) signupTab.addEventListener('click', () => switchTab('signup'));
    if (loginForm) loginForm.addEventListener('submit', handleLogin);
    if (signupForm) signupForm.addEventListener('submit', handleSignup);
    if (logoutBtn) logoutBtn.addEventListener('click', handleLogout);

    // Check authentication status on page load
    checkAuthStatus();
});

// Functions
function switchTab(tab) {
    // Update active tab
    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
    document.querySelector(`.tab[data-tab="${tab}"]`).classList.add('active');
    
    // Show the correct form
    loginForm.style.display = tab === 'login' ? 'block' : 'none';
    signupForm.style.display = tab === 'signup' ? 'block' : 'none';
}

async function handleLogin(e) {
    e.preventDefault();

    const email = document.getElementById('login-email').value;
    const password = document.getElementById('login-password').value;

    // Show loading state
    const submitBtn = loginForm.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Signing in...';
    submitBtn.disabled = true;

    try {
        const { data, error } = await supabaseClient.auth.signInWithPassword({
            email: email,
            password: password
        });

        if (error) {
            showError(loginForm, error.message);
        } else {
            // Successful login - redirect to dashboard
            window.location.href = 'dashboard.html';
        }
    } catch (error) {
        showError(loginForm, 'An unexpected error occurred. Please try again.');
        console.error('Login error:', error);
    } finally {
        // Reset button state
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }
}

async function handleSignup(e) {
    e.preventDefault();

    const name = document.getElementById('signup-name').value;
    const email = document.getElementById('signup-email').value;
    const password = document.getElementById('signup-password').value;
    const confirmPassword = document.getElementById('signup-confirm-password').value;

    // Validate passwords match
    if (password !== confirmPassword) {
        showError(signupForm, 'Passwords do not match');
        return;
    }

    // Validate password strength
    if (password.length < 6) {
        showError(signupForm, 'Password must be at least 6 characters long');
        return;
    }

    // Show loading state
    const submitBtn = signupForm.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Creating account...';
    submitBtn.disabled = true;

    try {
        const { data, error } = await supabaseClient.auth.signUp({
            email: email,
            password: password,
            options: {
                data: {
                    full_name: name
                }
            }
        });

        if (error) {
            showError(signupForm, error.message);
        } else {
            // Show success message and switch to login
            showSuccess(signupForm, 'Account created successfully! Please check your email to verify your account, then log in.');
            setTimeout(() => switchTab('login'), 3000);
        }
    } catch (error) {
        showError(signupForm, 'An unexpected error occurred. Please try again.');
        console.error('Signup error:', error);
    } finally {
        // Reset button state
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }
}

function showError(form, message) {
    // Remove any existing messages
    removeMessages(form);
    
    // Create and append error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.textContent = message;
    form.appendChild(errorDiv);
}

function showSuccess(form, message) {
    // Remove any existing messages
    removeMessages(form);
    
    // Create and append success message
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.textContent = message;
    form.appendChild(successDiv);
}

function removeMessages(form) {
    const messages = form.querySelectorAll('.error-message, .success-message');
    messages.forEach(msg => msg.remove());
}

// Handle logout
async function handleLogout() {
    try {
        const { error } = await supabaseClient.auth.signOut();
        if (error) {
            console.error('Logout error:', error);
        }
        // Redirect to login page
        window.location.href = 'index.html';
    } catch (error) {
        console.error('Logout error:', error);
        // Force redirect even if logout fails
        window.location.href = 'index.html';
    }
}

// Check authentication status
async function checkAuthStatus() {
    try {
        const { data: { session } } = await supabaseClient.auth.getSession();

        if (session) {
            // User is logged in
            if (window.location.pathname.includes('index.html') || window.location.pathname === '/') {
                window.location.href = 'dashboard.html';
            } else if (window.location.pathname.includes('dashboard.html')) {
                // Update user info in dashboard
                updateUserInfo(session.user);
            }
        } else {
            // User is not logged in
            if (window.location.pathname.includes('dashboard.html')) {
                window.location.href = 'index.html';
            }
        }
    } catch (error) {
        console.error('Auth check error:', error);
        // If there's an error checking auth, redirect to login
        if (window.location.pathname.includes('dashboard.html')) {
            window.location.href = 'index.html';
        }
    }
}

// Update user info in dashboard
function updateUserInfo(user) {
    const userNameElement = document.getElementById('user-name');
    if (userNameElement) {
        const displayName = user.user_metadata?.full_name || user.email;
        userNameElement.textContent = `Welcome, ${displayName}`;
    }
}

// Listen for auth state changes
supabaseClient.auth.onAuthStateChange((event, session) => {
    if (event === 'SIGNED_IN') {
        // User signed in
        if (window.location.pathname.includes('index.html') || window.location.pathname === '/') {
            window.location.href = 'dashboard.html';
        }
    } else if (event === 'SIGNED_OUT') {
        // User signed out
        if (window.location.pathname.includes('dashboard.html')) {
            window.location.href = 'index.html';
        }
    }
});