<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Generator - Database Setup</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .setup-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .setup-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 30px;
            margin-bottom: 20px;
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .setup-header h1 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .setup-step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            padding: 15px;
            border-radius: var(--border-radius);
            background-color: var(--background-color);
        }
        
        .step-number {
            background: var(--primary-color);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .step-content h3 {
            margin: 0 0 10px 0;
            color: var(--text-color);
        }
        
        .step-content p {
            margin: 0;
            color: var(--dark-gray);
            line-height: 1.5;
        }
        
        .status-indicator {
            margin-left: auto;
            padding: 5px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .setup-actions {
            text-align: center;
            margin-top: 30px;
        }
        
        .setup-actions button {
            margin: 0 10px;
        }
        
        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: var(--border-radius);
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            display: none;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-success {
            color: #28a745;
        }
        
        .log-error {
            color: #dc3545;
        }
        
        .log-info {
            color: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <div class="setup-card">
                <div class="setup-header">
                    <h1><i class="fas fa-database"></i> Database Setup</h1>
                    <p>Set up your Invoice Generator database tables and security policies</p>
                </div>
                
                <div class="setup-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>Check Supabase Connection</h3>
                        <p>Verify that your Supabase configuration is correct and the connection is working.</p>
                    </div>
                    <div class="status-indicator status-pending" id="connection-status">Pending</div>
                </div>
                
                <div class="setup-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>Create Database Tables</h3>
                        <p>Create the customers, invoices, and invoice_items tables with proper relationships.</p>
                    </div>
                    <div class="status-indicator status-pending" id="tables-status">Pending</div>
                </div>
                
                <div class="setup-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>Set Up Security Policies</h3>
                        <p>Configure Row Level Security (RLS) policies to ensure data privacy and security.</p>
                    </div>
                    <div class="status-indicator status-pending" id="security-status">Pending</div>
                </div>
                
                <div class="setup-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3>Create Helper Functions</h3>
                        <p>Set up database functions for invoice number generation and other utilities.</p>
                    </div>
                    <div class="status-indicator status-pending" id="functions-status">Pending</div>
                </div>
                
                <div class="setup-actions">
                    <button id="check-status-btn" class="btn-secondary">
                        <i class="fas fa-search"></i> Check Current Status
                    </button>
                    <button id="setup-database-btn" class="btn-primary">
                        <i class="fas fa-play"></i> Run Database Setup
                    </button>
                    <button id="continue-btn" class="btn-primary" style="display: none;" onclick="window.location.href='index.html'">
                        <i class="fas fa-arrow-right"></i> Continue to App
                    </button>
                </div>
                
                <div id="log-container" class="log-container">
                    <div id="setup-log"></div>
                </div>
            </div>
            
            <div class="setup-card">
                <h3><i class="fas fa-info-circle"></i> Manual Setup Alternative</h3>
                <p>If the automated setup doesn't work, you can manually run the SQL in your Supabase dashboard:</p>
                <ol>
                    <li>Go to your <a href="https://supabase.com" target="_blank">Supabase Dashboard</a></li>
                    <li>Navigate to <strong>Database > SQL Editor</strong></li>
                    <li>Copy and paste the SQL from <code>setup-instructions.md</code></li>
                    <li>Click "Run" to execute the SQL</li>
                </ol>
                <button class="btn-secondary" onclick="window.open('setup-instructions.md', '_blank')">
                    <i class="fas fa-file-alt"></i> View SQL Instructions
                </button>
            </div>
        </div>
    </div>
    
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/supabase-config.js"></script>
    <script src="js/database-setup.js"></script>
    <script>
        let dbSetup;
        
        document.addEventListener('DOMContentLoaded', () => {
            dbSetup = new DatabaseSetup(supabaseClient);
            
            document.getElementById('check-status-btn').addEventListener('click', checkStatus);
            document.getElementById('setup-database-btn').addEventListener('click', runSetup);
            
            // Check status on page load
            checkStatus();
        });
        
        async function checkStatus() {
            updateStatus('connection-status', 'pending', 'Checking...');
            
            try {
                // Check Supabase connection
                const { data: { user } } = await supabaseClient.auth.getUser();
                updateStatus('connection-status', 'success', 'Connected');
                
                // Check if tables exist
                const tablesExist = await dbSetup.checkTablesExist();
                
                updateStatus('tables-status', 
                    tablesExist.customers && tablesExist.invoices && tablesExist.invoice_items ? 'success' : 'pending',
                    tablesExist.customers && tablesExist.invoices && tablesExist.invoice_items ? 'Created' : 'Not Created'
                );
                
                // If all tables exist, assume setup is complete
                if (tablesExist.customers && tablesExist.invoices && tablesExist.invoice_items) {
                    updateStatus('security-status', 'success', 'Configured');
                    updateStatus('functions-status', 'success', 'Created');
                    document.getElementById('continue-btn').style.display = 'inline-block';
                }
                
            } catch (error) {
                updateStatus('connection-status', 'error', 'Failed');
                console.error('Status check error:', error);
            }
        }
        
        async function runSetup() {
            const setupBtn = document.getElementById('setup-database-btn');
            const logContainer = document.getElementById('log-container');
            const setupLog = document.getElementById('setup-log');
            
            setupBtn.disabled = true;
            setupBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Setting up...';
            
            logContainer.style.display = 'block';
            setupLog.innerHTML = '';
            
            // Override console.log to capture logs
            const originalLog = console.log;
            console.log = function(...args) {
                originalLog.apply(console, args);
                addLogEntry('info', args.join(' '));
            };
            
            const originalError = console.error;
            console.error = function(...args) {
                originalError.apply(console, args);
                addLogEntry('error', args.join(' '));
            };
            
            try {
                const result = await dbSetup.setupDatabase();
                
                if (result.success) {
                    addLogEntry('success', result.message);
                    updateStatus('tables-status', 'success', 'Created');
                    updateStatus('security-status', 'success', 'Configured');
                    updateStatus('functions-status', 'success', 'Created');
                    document.getElementById('continue-btn').style.display = 'inline-block';
                } else {
                    addLogEntry('error', result.error);
                    updateStatus('tables-status', 'error', 'Failed');
                }
                
            } catch (error) {
                addLogEntry('error', 'Setup failed: ' + error.message);
                updateStatus('tables-status', 'error', 'Failed');
            } finally {
                setupBtn.disabled = false;
                setupBtn.innerHTML = '<i class="fas fa-play"></i> Run Database Setup';
                
                // Restore console functions
                console.log = originalLog;
                console.error = originalError;
            }
        }
        
        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.className = `status-indicator status-${status}`;
            element.textContent = text;
        }
        
        function addLogEntry(type, message) {
            const setupLog = document.getElementById('setup-log');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            setupLog.appendChild(entry);
            setupLog.scrollTop = setupLog.scrollHeight;
        }
    </script>
</body>
</html>
