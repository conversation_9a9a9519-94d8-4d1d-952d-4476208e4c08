// DOM Elements
const taskForm = document.getElementById('task-form');
const taskList = document.getElementById('task-list');
const noTasksMessage = document.getElementById('no-tasks-message');
const userNameElement = document.getElementById('user-name');
const logoutBtn = document.getElementById('logout-btn');
const filterButtons = document.querySelectorAll('#tasks-filter .tab');

// Event Listeners
taskForm.addEventListener('submit', addTask);
logoutBtn.addEventListener('click', logout);
filterButtons.forEach(btn => btn.addEventListener('click', filterTasks));

// Load user info and tasks when page loads
document.addEventListener('DOMContentLoaded', () => {
    loadUserInfo();
    loadTasks();
});

// Functions
function loadUserInfo() {
    const currentUser = JSON.parse(localStorage.getItem('currentUser'));
    
    // Redirect to login if not logged in
    if (!currentUser) {
        window.location.href = 'index.html';
        return;
    }
    
    // Display user name
    userNameElement.textContent = `Welcome, ${currentUser.name}`;
}

function logout() {
    // Remove current user from localStorage
    localStorage.removeItem('currentUser');
    
    // Redirect to login page
    window.location.href = 'index.html';
}

function loadTasks() {
    const currentUser = JSON.parse(localStorage.getItem('currentUser'));
    if (!currentUser) return;
    
    // Get tasks for current user
    const allTasks = JSON.parse(localStorage.getItem('tasks') || '[]');
    const userTasks = allTasks.filter(task => task.userEmail === currentUser.email);
    
    // Update UI based on tasks
    if (userTasks.length === 0) {
        noTasksMessage.style.display = 'block';
        taskList.innerHTML = '';
    } else {
        noTasksMessage.style.display = 'none';
        renderTasks(userTasks);
    }
}

function renderTasks(tasks) {
    // Clear current task list
    taskList.innerHTML = '';
    
    // Get current filter
    const currentFilter = document.querySelector('#tasks-filter .tab.active').dataset.filter;
    
    // Filter tasks based on current filter
    let filteredTasks = tasks;
    if (currentFilter === 'active') {
        filteredTasks = tasks.filter(task => !task.completed);
    } else if (currentFilter === 'completed') {
        filteredTasks = tasks.filter(task => task.completed);
    }
    
    // Sort tasks by deadline (closest first)
    filteredTasks.sort((a, b) => new Date(a.deadline) - new Date(b.deadline));
    
    // Create task elements
    filteredTasks.forEach(task => {
        const taskItem = document.createElement('li');
        taskItem.className = `task-item ${task.completed ? 'completed' : ''}`;
        taskItem.dataset.id = task.id;
        
        // Format deadline date
        const deadlineDate = new Date(task.deadline);
        const formattedDate = deadlineDate.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'short', 
            day: 'numeric' 
        });
        
        // Calculate if task is overdue
        const isOverdue = !task.completed && deadlineDate < new Date() && deadlineDate.toDateString() !== new Date().toDateString();
        
        taskItem.innerHTML = `
            <div class="task-info">
                <div class="task-title">${task.title}</div>
                <div class="task-deadline ${isOverdue ? 'error-message' : ''}">
                    <i class="fas fa-calendar-alt"></i> 
                    ${isOverdue ? 'Overdue! ' : ''}Due: ${formattedDate}
                </div>
            </div>
            <div class="task-actions">
                <button class="task-btn complete-btn" title="${task.completed ? 'Mark as incomplete' : 'Mark as complete'}">
                    <i class="fas ${task.completed ? 'fa-times-circle' : 'fa-check-circle'}"></i>
                </button>
                <button class="task-btn delete-btn" title="Delete task">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        
        // Add event listeners to buttons
        const completeBtn = taskItem.querySelector('.complete-btn');
        const deleteBtn = taskItem.querySelector('.delete-btn');
        
        completeBtn.addEventListener('click', () => toggleTaskComplete(task.id));
        deleteBtn.addEventListener('click', () => deleteTask(task.id));
        
        taskList.appendChild(taskItem);
    });
    
    // Show message if no tasks match the filter
    if (filteredTasks.length === 0) {
        const message = document.createElement('div');
        message.className = 'text-center mt-20';
        message.innerHTML = `<p>No ${currentFilter !== 'all' ? currentFilter : ''} tasks found.</p>`;
        taskList.appendChild(message);
    }
}

function addTask(e) {
    e.preventDefault();
    
    const currentUser = JSON.parse(localStorage.getItem('currentUser'));
    if (!currentUser) return;
    
    const title = document.getElementById('task-title').value.trim();
    const deadline = document.getElementById('task-deadline').value;
    
    if (!title || !deadline) return;
    
    // Create new task object
    const newTask = {
        id: Date.now().toString(),
        title,
        deadline,
        completed: false,
        userEmail: currentUser.email,
        createdAt: new Date().toISOString()
    };
    
    // Add to localStorage
    const allTasks = JSON.parse(localStorage.getItem('tasks') || '[]');
    allTasks.push(newTask);
    localStorage.setItem('tasks', JSON.stringify(allTasks));
    
    // Reset form
    taskForm.reset();
    
    // Reload tasks
    loadTasks();
    
    // Show success message
    showNotification('Task added successfully!');
}

function toggleTaskComplete(taskId) {
    const allTasks = JSON.parse(localStorage.getItem('tasks') || '[]');
    const taskIndex = allTasks.findIndex(task => task.id === taskId);
    
    if (taskIndex !== -1) {
        // Toggle completed status
        allTasks[taskIndex].completed = !allTasks[taskIndex].completed;
        localStorage.setItem('tasks', JSON.stringify(allTasks));
        
        // Reload tasks
        loadTasks();
        
        // Show notification
        const status = allTasks[taskIndex].completed ? 'completed' : 'marked as active';
        showNotification(`Task ${status}!`);
    }
}

function deleteTask(taskId) {
    if (!confirm('Are you sure you want to delete this task?')) return;
    
    const allTasks = JSON.parse(localStorage.getItem('tasks') || '[]');
    const updatedTasks = allTasks.filter(task => task.id !== taskId);
    
    localStorage.setItem('tasks', JSON.stringify(updatedTasks));
    
    // Reload tasks
    loadTasks();
    
    // Show notification
    showNotification('Task deleted!');
}

function filterTasks(e) {
    // Update active filter button
    filterButtons.forEach(btn => btn.classList.remove('active'));
    e.target.classList.add('active');
    
    // Reload tasks with new filter
    loadTasks();
}

function showNotification(message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    
    // Add to body
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('hide');
        setTimeout(() => notification.remove(), 500);
    }, 3000);
}

// Add notification styles
const style = document.createElement('style');
style.textContent = `
    .notification {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background-color: var(--primary-color);
        color: white;
        padding: 12px 20px;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        z-index: 1000;
        opacity: 1;
        transform: translateY(0);
        transition: opacity 0.3s, transform 0.3s;
    }
    
    .notification.hide {
        opacity: 0;
        transform: translateY(10px);
    }
`;
document.head.appendChild(style);