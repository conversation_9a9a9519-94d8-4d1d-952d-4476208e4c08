# Task Manager Web Application

## Overview
A modern task management web application built with HTML, CSS, and JavaScript. This application allows users to create an account, log in, add tasks with titles and deadlines, mark tasks as completed, and view their task list.

## Features
- User authentication (signup and login)
- Task management (add, complete, delete tasks)
- Task filtering (all, active, completed)
- Responsive design for all devices
- Clean, modern UI
- Local storage for data persistence

## Project Structure
- `index.html` - Login/signup page
- `dashboard.html` - Task management dashboard
- `css/styles.css` - Styling for the application
- `js/auth.js` - Authentication functionality
- `js/tasks.js` - Task management functionality

## How to Use
1. Open `index.html` in your browser
2. Create an account or log in
3. Add tasks with titles and deadlines
4. Mark tasks as completed or delete them
5. Filter tasks by status (all, active, completed)

## Technologies Used
- HTML5
- CSS3
- JavaScript (ES6+)
- Local Storage API for data persistence
- Font Awesome for icons

## Browser Compatibility
This application is compatible with all modern browsers including:
- Google Chrome
- Mozilla Firefox
- Microsoft Edge
- Safari

## Future Enhancements
- Task categories/tags
- Task priority levels
- Task notes/descriptions
- Dark mode toggle
- Data export/import
- Cloud synchronization