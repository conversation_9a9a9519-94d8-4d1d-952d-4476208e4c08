# Professional Service Website

## Overview
This is a modern, professional service website for a company offering four services. The website includes a homepage, detailed service pages, contact page, and terms and conditions page. Two of the services have "Buy Now" buttons with Stripe payment integration, while the other two have "Request a Quote" buttons that open a form for users to enter their details.

## Features
- Responsive design for mobile and desktop
- Clean, professional layout with card-style service presentation
- Online payment functionality using Stripe
- Quote request form with address, phone, and preferred date/time fields
- Contact form for general inquiries
- Terms and Conditions page
- Basic header and footer with navigation

## Project Structure
```
Service Website/
├── css/
│   └── styles.css          # Main stylesheet
├── js/
│   ├── main.js             # General site functionality
│   └── payment.js          # Stripe payment integration
├── index.html              # Homepage
├── services.html           # Services page with payment and quote options
├── contact.html            # Contact page
├── terms.html              # Terms and Conditions page
└── README.md               # Project documentation
```

## Setup Instructions

### Prerequisites
- Web server (local or hosted)
- Stripe account for payment processing (for production use)

### Stripe Integration
1. Create a Stripe account at [stripe.com](https://stripe.com)
2. Replace the test publishable key in `js/payment.js` with your actual key
3. Implement server-side payment processing (not included in this demo)

### Local Development
1. Clone or download the repository
2. Open the project in your code editor
3. Serve the files using a local web server
4. Access the site via `http://localhost` or your local server URL

## Customization

### Changing Services
1. Edit the service cards in `index.html`
2. Update the detailed service information in `services.html`
3. Adjust pricing and service features as needed

### Styling
1. Modify `css/styles.css` to change colors, fonts, and layout
2. Update the hero image by changing the background URL in the `.hero` class

### Company Information
1. Update company name, contact details, and address throughout the site
2. Modify the Terms and Conditions in `terms.html` to match your business policies

## Notes
- The payment functionality is set up for demonstration purposes only
- In a production environment, you would need to implement server-side payment processing
- The forms currently use client-side validation and alerts for demonstration
- For production, implement proper form handling and data storage